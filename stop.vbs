' 函数：TerminateProcess
' 描述：根据进程名终止进程
' 参数：processName - 要结束的进程的名称
Function TerminateProcess(processName)
    ' 定义本地计算机
    strComputer = "."

    ' 获取 WMI 服务对象
    Set objWMIService = GetObject("winmgmts:\\" & strComputer & "\root\cimv2")
	' 获取 WMI 服务对象 备份
	' Set objWMIService = GetObject("winmgmts:{impersonationLevel=impersonate}!\\.\root\cimv2")

    ' 查询指定名称的进程
    Set colProcessList = objWMIService.ExecQuery("SELECT * FROM Win32_Process WHERE Name='" & processName & "'")

    ' 遍历匹配的进程并结束它们
    For Each objProcess in colProcessList
        objProcess.Terminate()
    Next
End Function

' 调用函数来结束指定进程
TerminateProcess "warp-plus-0.exe"
TerminateProcess("warp-plus-1.exe")
Call TerminateProcess("warp-plus-2.exe")
Call TerminateProcess("warp-plus-3.exe")